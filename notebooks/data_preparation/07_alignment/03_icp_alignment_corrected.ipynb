{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# ICP Point Cloud Alignment with Coordinate Correction\n", "\n", "This notebook implements ICP alignment using coordinate-corrected point clouds to resolve the alignment issues identified in the original ICP notebook.\n", "\n", "**Key Improvements:**\n", "- Uses coordinate-corrected point clouds (if available)\n", "- Resolves 74.3m horizontal and 154.9m vertical offsets\n", "- Expected RMSE improvement: 6.64-19.37m → <1.0m\n", "- Maintains compatibility with original files as fallback\n", "\n", "**Comparison with Original:**\n", "- `01_icp_alignment.ipynb`: Shows original issues (RMSE 6.64-19.37m)\n", "- `02_icp_alignment_corrected.ipynb`: This notebook with fixes\n", "\n", "**Author**: <PERSON><PERSON><PERSON>  \n", "**Date**: July 2025"]}, {"cell_type": "code", "execution_count": 14, "metadata": {"tags": ["parameters"]}, "outputs": [], "source": ["# Parameters (Papermill)\n", "ground_method = \"csf\"  # Ground segmentation method: csf, pmf, ransac\n", "site_name = \"trino_enel\"\n", "icp_max_iterations = 50\n", "icp_tolerance = 1e-6\n", "voxel_size = 0.02  # For downsampling if needed\n", "output_dir = \"../../../data/output_runs/icp_alignment_corrected\"\n", "enable_visualization = True\n", "save_results = True\n", "use_coordinate_correction = True  # Enable coordinate correction"]}, {"cell_type": "code", "execution_count": 15, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["ICP ALIGNMENT WITH COORDINATE CORRECTION - CSF\n", "Site: trino_enel\n", "Output: ../../../data/output_runs/icp_alignment_corrected/csf\n", "Coordinate correction: Enabled\n", "Timestamp: 2025-07-09 19:49:15\n"]}], "source": ["# Imports\n", "import numpy as np\n", "import open3d as o3d\n", "import matplotlib.pyplot as plt\n", "import pandas as pd\n", "import time\n", "import json\n", "from pathlib import Path\n", "from datetime import datetime\n", "from scipy.spatial import cKDTree\n", "\n", "# Setup\n", "np.random.seed(42)\n", "output_path = Path(output_dir) / ground_method\n", "output_path.mkdir(parents=True, exist_ok=True)\n", "\n", "print(f\"ICP ALIGNMENT WITH COORDINATE CORRECTION - {ground_method.upper()}\")\n", "print(f\"Site: {site_name}\")\n", "print(f\"Output: {output_path}\")\n", "print(f\"Coordinate correction: {'Enabled' if use_coordinate_correction else '❌ Disabled'}\")\n", "print(f\"Timestamp: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\")"]}, {"cell_type": "code", "execution_count": 16, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["total 0\n", "drwxr-xr-x@  5 <USER>  <GROUP>   160B Jul  5 21:31 \u001b[34madvanced_ifc_metadata\u001b[m\u001b[m\n", "drwxr-xr-x@  6 <USER>  <GROUP>   192B Jul  9 13:03 \u001b[34maligned_coordinates\u001b[m\u001b[m\n", "drwxr-xr-x@  2 <USER>  <GROUP>    64B Jul  4 17:11 \u001b[34manalysis\u001b[m\u001b[m\n", "drwxr-xr-x@  4 <USER>  <GROUP>   128B Jul  3 20:07 \u001b[34mdenoising\u001b[m\u001b[m\n", "drwxr-xr-x@  6 <USER>  <GROUP>   192B Jul  4 15:07 \u001b[34mground_segmentation\u001b[m\u001b[m\n", "drwxr-xr-x@ 12 <USER>  <GROUP>   384B Jul  4 16:34 \u001b[34mifc_metadata\u001b[m\u001b[m\n", "drwxr-xr-x@  9 <USER>  <GROUP>   288B Jul  6 13:15 \u001b[34mifc_pointclouds\u001b[m\u001b[m\n", "drwxr-xr-x@  2 <USER>  <GROUP>    64B Jul  4 16:35 \u001b[34mvalidation\u001b[m\u001b[m\n"]}], "source": ["!ls -lh ../../../data/processed/trino_enel"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 1. Load Data with Coordinate Correction Support"]}, {"cell_type": "code", "execution_count": 17, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Using coordinate-corrected point clouds\n", "  Drone (corrected): ../../../data/processed/trino_enel/aligned_coordinates/trino_enel_drone_csf_corrected.ply\n", "  IFC (corrected): ../../../data/processed/trino_enel/aligned_coordinates/trino_enel_ifc_corrected.ply\n", "\n", "Loading point clouds...\n", "Drone exists: True\n", "IFC exists: True\n"]}], "source": ["# Define file paths - Use corrected coordinates if available\n", "if use_coordinate_correction:\n", "    corrected_drone_file = Path(f\"../../../data/processed/{site_name}/aligned_coordinates/{site_name}_drone_{ground_method}_corrected.ply\")\n", "    corrected_ifc_file = Path(f\"../../../data/processed/{site_name}/aligned_coordinates/{site_name}_ifc_corrected.ply\")\n", "    \n", "    # Use corrected files if they exist, otherwise fall back to original\n", "    if corrected_drone_file.exists() and corrected_ifc_file.exists():\n", "        drone_file = corrected_drone_file\n", "        ifc_file = corrected_ifc_file\n", "        coordinate_status = \"Using coordinate-corrected point clouds\"\n", "        print(coordinate_status)\n", "        print(f\"  Drone (corrected): {drone_file}\")\n", "        print(f\"  IFC (corrected): {ifc_file}\")\n", "    else:\n", "        drone_file = Path(f\"../../data/processed/{site_name}/ground_segmentation/{ground_method}/trino_enel_nonground.ply\")\n", "        ifc_file = Path(f\"../../data/processed/{site_name}/ifc_pointclouds/GRE.EEC.S.00.IT.P.14353.00.265_data_driven.ply\")\n", "        coordinate_status = \"Corrected files not found - using original point clouds\"\n", "        print(coordinate_status)\n", "        print(f\"  Run 01_coordinate_correction.ipynb first for better results\")\n", "        print(f\"  Drone (original): {drone_file}\")\n", "        print(f\"  IFC (original): {ifc_file}\")\n", "else:\n", "    # Use original files\n", "    drone_file = Path(f\"../../../data/processed/{site_name}/ground_segmentation/{ground_method}/trino_enel_nonground.ply\")\n", "    ifc_file = Path(f\"../../../data/processed/{site_name}/ifc_pointclouds/GRE.EEC.S.00.IT.P.14353.00.265_data_driven.ply\")\n", "    coordinate_status = \"Using original point clouds (coordinate correction disabled)\"\n", "    print(coordinate_status)\n", "\n", "print(f\"\\nLoading point clouds...\")\n", "print(f\"Drone exists: {drone_file.exists()}\")\n", "print(f\"IFC exists: {ifc_file.exists()}\")"]}, {"cell_type": "code", "execution_count": 18, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Loaded drone scan: 13,848 points\n", "Loaded IFC model: 1,359,240 points\n"]}], "source": ["# Load point clouds\n", "if drone_file.exists() and ifc_file.exists():\n", "    drone_pcd = o3d.io.read_point_cloud(str(drone_file))\n", "    ifc_pcd = o3d.io.read_point_cloud(str(ifc_file))\n", "    \n", "    drone_points = np.asarray(drone_pcd.points)\n", "    ifc_points = np.asarray(ifc_pcd.points)\n", "    \n", "    print(f\"Loaded drone scan: {drone_points.shape[0]:,} points\")\n", "    print(f\"Loaded IFC model: {ifc_points.shape[0]:,} points\")\n", "    \n", "    # Store original for comparison\n", "    drone_pcd_original = drone_pcd\n", "    ifc_pcd_original = ifc_pcd\n", "    \n", "else:\n", "    print(\"Error: Required point cloud files not found!\")\n", "    if not drone_file.exists():\n", "        print(f\"  Missing: {drone_file}\")\n", "    if not ifc_file.exists():\n", "        print(f\"  Missing: {ifc_file}\")\n", "    raise FileNotFoundError(\"Missing point cloud files\")"]}, {"cell_type": "code", "execution_count": 19, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\n", "POINT CLOUD STATISTICS\n", "\n", "Drone scan (ground truth):\n", "  Points: 13,848\n", "  X range: [435223.72, 436794.15]\n", "  Y range: [5010816.92, 5012539.06]\n", "  Z range: [1.17, 13.14]\n", "\n", "IFC model (to be aligned):\n", "  Points: 1,359,240\n", "  X range: [435305.52, 436758.33]\n", "  Y range: [5010837.09, 5012398.83]\n", "  Z range: [-2.00, 6.79]\n", "\n", "INITIAL COORDINATE ANALYSIS\n", "Drone center: [436024.70, 5011683.28, 2.46]\n", "IFC center:   [436024.70, 5011683.28, 2.47]\n", "Initial offset: [-0.00, -0.00, -0.00]\n", "Offset magnitude: 0.00 meters\n", "Good coordinate alignment (offset: 0.00m)\n"]}], "source": ["# Display initial statistics and coordinate analysis\n", "print(\"\\nPOINT CLOUD STATISTICS\")\n", "print(\"\\nDrone scan (ground truth):\")\n", "print(f\"  Points: {drone_points.shape[0]:,}\")\n", "print(f\"  X range: [{drone_points[:, 0].min():.2f}, {drone_points[:, 0].max():.2f}]\")\n", "print(f\"  Y range: [{drone_points[:, 1].min():.2f}, {drone_points[:, 1].max():.2f}]\")\n", "print(f\"  Z range: [{drone_points[:, 2].min():.2f}, {drone_points[:, 2].max():.2f}]\")\n", "\n", "print(\"\\nIFC model (to be aligned):\")\n", "print(f\"  Points: {ifc_points.shape[0]:,}\")\n", "print(f\"  X range: [{ifc_points[:, 0].min():.2f}, {ifc_points[:, 0].max():.2f}]\")\n", "print(f\"  Y range: [{ifc_points[:, 1].min():.2f}, {ifc_points[:, 1].max():.2f}]\")\n", "print(f\"  Z range: [{ifc_points[:, 2].min():.2f}, {ifc_points[:, 2].max():.2f}]\")\n", "\n", "# Calculate initial offset\n", "drone_center = drone_points.mean(axis=0)\n", "ifc_center = ifc_points.mean(axis=0)\n", "initial_offset = drone_center - ifc_center\n", "\n", "print(f\"\\nINITIAL COORDINATE ANALYSIS\")\n", "print(f\"Drone center: [{drone_center[0]:.2f}, {drone_center[1]:.2f}, {drone_center[2]:.2f}]\")\n", "print(f\"IFC center:   [{ifc_center[0]:.2f}, {ifc_center[1]:.2f}, {ifc_center[2]:.2f}]\")\n", "print(f\"Initial offset: [{initial_offset[0]:.2f}, {initial_offset[1]:.2f}, {initial_offset[2]:.2f}]\")\n", "print(f\"Offset magnitude: {np.linalg.norm(initial_offset):.2f} meters\")\n", "\n", "# Assess coordinate compatibility\n", "offset_magnitude = np.linalg.norm(initial_offset)\n", "if offset_magnitude < 10:\n", "    print(f\"Good coordinate alignment (offset: {offset_magnitude:.2f}m)\")\n", "elif offset_magnitude < 100:\n", "    print(f\"Moderate coordinate offset (offset: {offset_magnitude:.2f}m)\")\n", "else:\n", "    print(f\"Large coordinate offset (offset: {offset_magnitude:.2f}m)\")\n", "    print(f\"   Consider running coordinate correction first\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 2. ICP Alignment Implementation"]}, {"cell_type": "code", "execution_count": 20, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\n", "PREPARING POINT CLOUDS FOR ICP\n", "Source (IFC): 1,359,240 points\n", "Target (Drone): 13,848 points\n", "\n", "DOWNSAMPLING (voxel size: 0.02m)\n", "After downsampling - Source: 1,331,779, Target: 13,848\n"]}], "source": ["# Convert to Open3D point clouds\n", "print(\"\\nPREPARING POINT CLOUDS FOR ICP\")\n", "\n", "# Create Open3D point clouds\n", "source_pcd = o3d.geometry.PointCloud()\n", "source_pcd.points = o3d.utility.Vector3dVector(ifc_points)\n", "\n", "target_pcd = o3d.geometry.PointCloud()\n", "target_pcd.points = o3d.utility.Vector3dVector(drone_points)\n", "\n", "print(f\"Source (IFC): {len(source_pcd.points):,} points\")\n", "print(f\"Target (Drone): {len(target_pcd.points):,} points\")\n", "\n", "# Downsample if needed\n", "if voxel_size > 0:\n", "    print(f\"\\nDOWNSAMPLING (voxel size: {voxel_size}m)\")\n", "    source_pcd = source_pcd.voxel_down_sample(voxel_size)\n", "    target_pcd = target_pcd.voxel_down_sample(voxel_size)\n", "    print(f\"After downsampling - Source: {len(source_pcd.points):,}, Target: {len(target_pcd.points):,}\")"]}, {"cell_type": "code", "execution_count": 21, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\n", "RUNNING ICP ALIGNMENT\n", "Max iterations: 50\n", "Tolerance: 1e-06\n", "\n", "ICP COMPLETED in 4.79 seconds\n", "Fitness: 0.024276\n", "Inlier RMSE: 1.497606\n", "Correspondence set size: 32330\n"]}], "source": ["# Run ICP alignment\n", "print(f\"\\nRUNNING ICP ALIGNMENT\")\n", "print(f\"Max iterations: {icp_max_iterations}\")\n", "print(f\"Tolerance: {icp_tolerance}\")\n", "\n", "# Initial transformation (identity)\n", "init_transformation = np.eye(4)\n", "\n", "# Run ICP\n", "import time\n", "start_time = time.time()\n", "\n", "icp_result = o3d.pipelines.registration.registration_icp(\n", "    source_pcd, target_pcd,\n", "    max_correspondence_distance=2.0,\n", "    init=init_transformation,\n", "    estimation_method=o3d.pipelines.registration.TransformationEstimationPointToPoint(),\n", "    criteria=o3d.pipelines.registration.ICPConvergenceCriteria(\n", "        max_iteration=icp_max_iterations,\n", "        relative_fitness=icp_tolerance,\n", "        relative_rmse=icp_tolerance\n", "    )\n", ")\n", "\n", "icp_time = time.time() - start_time\n", "\n", "print(f\"\\nICP COMPLETED in {icp_time:.2f} seconds\")\n", "print(f\"Fitness: {icp_result.fitness:.6f}\")\n", "print(f\"Inlier RMSE: {icp_result.inlier_rmse:.6f}\")\n", "print(f\"Correspondence set size: {len(icp_result.correspondence_set)}\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\n", "ALIGNMENT RESULTS\n", "RMSE: 19.367 m\n", "Mean distance: 14.486 m\n", "Max distance: 97.699 m\n", "ICP Fitness: 0.024276\n", "ICP Inlier RMSE: 1.497606 m\n", "\n", "TRANSFORMATION MATRIX:\n", "[[ 9.99999241e-01  1.32212785e-04 -1.22482312e-03 -6.60767611e+02]\n", " [-1.30324960e-04  9.99998804e-01  1.54125575e-03  6.32005089e+01]\n", " [ 1.22502543e-03 -1.54109495e-03  9.99998062e-01  7.18934564e+03]\n", " [ 0.00000000e+00  0.00000000e+00  0.00000000e+00  1.00000000e+00]]\n"]}], "source": ["# Apply transformation and calculate metrics\n", "print(f\"\\nALIGNMENT RESULTS\")\n", "\n", "# Apply transformation to original IFC points\n", "ifc_points_homogeneous = np.hstack([ifc_points, np.ones((ifc_points.shape[0], 1))])\n", "aligned_ifc_points = (icp_result.transformation @ ifc_points_homogeneous.T).T[:, :3]\n", "\n", "# Calculate RMSE using nearest neighbor distances\n", "from scipy.spatial import cKDTree\n", "tree = cKDTree(drone_points)\n", "distances, _ = tree.query(aligned_ifc_points)\n", "\n", "rmse = np.sqrt(np.mean(distances**2))\n", "mean_distance = np.mean(distances)\n", "max_distance = np.max(distances)\n", "\n", "print(f\"RMSE: {rmse:.3f} m\")\n", "print(f\"Mean distance: {mean_distance:.3f} m\")\n", "print(f\"Max distance: {max_distance:.3f} m\")\n", "print(f\"ICP Fitness: {icp_result.fitness:.6f}\")\n", "print(f\"ICP Inlier RMSE: {icp_result.inlier_rmse:.6f} m\")\n", "\n", "# Transformation matrix\n", "print(f\"\\nTRANSFORMATION MATRIX:\")\n", "print(icp_result.transformation)"]}, {"cell_type": "code", "execution_count": 23, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\n", "SAVING RESULTS\n", "Aligned point cloud: ../../../data/output_runs/icp_alignment_corrected/csf/aligned_ifc_csf.ply\n", "Transformation matrix: ../../../data/output_runs/icp_alignment_corrected/csf/transformation_matrix_csf.npy\n", "Metrics: ../../../data/output_runs/icp_alignment_corrected/csf/corrected_icp_metrics_csf.json\n", "\n", "CORRECTED ICP ALIGNMENT COMPLETED\n", "Expected improvement: 6.64-19.37m → 19.367m\n", "NEEDS WORK: Still high RMSE, coordinate correction may be needed\n"]}], "source": ["# Save results\n", "if save_results:\n", "    print(f\"\\nSAVING RESULTS\")\n", "    \n", "    # Save aligned point cloud\n", "    aligned_pcd = o3d.geometry.PointCloud()\n", "    aligned_pcd.points = o3d.utility.Vector3dVector(aligned_ifc_points)\n", "    aligned_file = output_path / f\"aligned_ifc_{ground_method}.ply\"\n", "    o3d.io.write_point_cloud(str(aligned_file), aligned_pcd)\n", "    print(f\"Aligned point cloud: {aligned_file}\")\n", "    \n", "    # Save transformation matrix\n", "    transform_file = output_path / f\"transformation_matrix_{ground_method}.npy\"\n", "    np.save(transform_file, icp_result.transformation)\n", "    print(f\"Transformation matrix: {transform_file}\")\n", "    \n", "    # Save metrics\n", "    import json\n", "    metrics = {\n", "        'ground_method': ground_method,\n", "        'site_name': site_name,\n", "        'rmse': float(rmse),\n", "        'mean_distance': float(mean_distance),\n", "        'max_distance': float(max_distance),\n", "        'icp_fitness': float(icp_result.fitness),\n", "        'icp_inlier_rmse': float(icp_result.inlier_rmse),\n", "        'icp_time': float(icp_time),\n", "        'correspondence_count': len(icp_result.correspondence_set),\n", "        'source_points': len(source_pcd.points),\n", "        'target_points': len(target_pcd.points),\n", "        'voxel_size': voxel_size,\n", "        'max_iterations': icp_max_iterations,\n", "        'tolerance': icp_tolerance,\n", "        'coordinate_correction_used': use_coordinate_correction,\n", "        'timestamp': datetime.now().isoformat()\n", "    }\n", "    \n", "    metrics_file = output_path / f\"corrected_icp_metrics_{ground_method}.json\"\n", "    with open(metrics_file, 'w') as f:\n", "        json.dump(metrics, f, indent=2)\n", "    print(f\"Metrics: {metrics_file}\")\n", "\n", "print(f\"\\nCORRECTED ICP ALIGNMENT COMPLETED\")\n", "print(f\"Expected improvement: 6.64-19.37m → {rmse:.3f}m\")\n", "if rmse < 1.0:\n", "    print(f\"SUCCESS: Achieved target RMSE < 1.0m\")\n", "elif rmse < 3.0:\n", "    print(f\"PARTIAL SUCCESS: Significant improvement but not optimal\")\n", "else:\n", "    print(f\"NEEDS WORK: Still high RMSE, coordinate correction may be needed\")"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.11"}}, "nbformat": 4, "nbformat_minor": 4}